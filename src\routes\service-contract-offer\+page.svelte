<script>
  import { enhance } from '$app/forms';
  import { goto } from '$app/navigation';
  import { page } from '$app/stores';
  import ServiceOfferItemForm from '$lib/components/ServiceOfferItemForm.svelte';

  /** @type {import('./$types').PageData} */
  export let data;

  // Component state
  let offerItems = data.serviceItems || [];
  let showItemForm = false;
  let editingItem = null;
  let isLoading = false;
  let error = null;
  let success = null;

  // Offer metadata
  let offerNotes = data.offer?.notes || '';
  let offerStatus = data.offer?.status || 'Draft';

  // Computed values
  $: enabledItems = offerItems.filter(item => item.isEnabled);
  $: totalAmount = enabledItems.reduce((sum, item) => sum + (item.totalPrice || 0), 0);
  $: requiredItems = enabledItems.filter(item => item.isRequired);
  $: optionalItems = enabledItems.filter(item => !item.isRequired);

  // Toggle item enabled/disabled
  function toggleItemEnabled(itemId) {
    offerItems = offerItems.map(item =>
      item._id === itemId
        ? { ...item, isEnabled: !item.isEnabled }
        : item
    );
  }

  // Edit item
  function editItem(item) {
    editingItem = { ...item };
    showItemForm = true;
  }

  // Add new item
  function addNewItem() {
    editingItem = {
      _id: `new-${Date.now()}`,
      serviceCode: '',
      description: '',
      activity: '',
      quantity: 1,
      unitPrice: 0,
      totalPrice: 0,
      isEnabled: true,
      isRequired: false,
      category: 'Custom',
      notes: '',
      source: 'Manual'
    };
    showItemForm = true;
  }

  // Save item changes
  function saveItem(updatedItem) {
    if (updatedItem._id.startsWith('new-')) {
      // Add new item
      updatedItem._id = `item-${Date.now()}`;
      offerItems = [...offerItems, updatedItem];
    } else {
      // Update existing item
      offerItems = offerItems.map(item =>
        item._id === updatedItem._id ? updatedItem : item
      );
    }
    showItemForm = false;
    editingItem = null;
  }

  // Remove item
  function removeItem(itemId) {
    if (confirm('Are you sure you want to remove this item?')) {
      offerItems = offerItems.filter(item => item._id !== itemId);
    }
  }

  // Save offer
  async function saveOffer() {
    isLoading = true;
    error = null;
    success = null;

    try {
      const offerData = {
        _id: data.offer?._id,
        customerId: data.customer._id,
        computerId: data.computer._id,
        items: offerItems,
        totalAmount,
        notes: offerNotes
      };

      const formData = new FormData();
      formData.append('offerData', JSON.stringify(offerData));

      const action = data.offer ? 'updateOffer' : 'createOffer';
      const response = await fetch(`?/${action}`, {
        method: 'POST',
        body: formData
      });

      const result = await response.json();

      if (result.success) {
        success = data.offer ? 'Offer updated successfully' : `Offer created successfully (${result.offerNumber})`;
        if (!data.offer) {
          // Redirect to the created offer
          goto(`/service-contract-offer?computerId=${data.computer._id}&offerId=${result.offerId}`);
        }
      } else {
        error = result.error || 'Failed to save offer';
      }
    } catch (err) {
      console.error('Error saving offer:', err);
      error = 'Failed to save offer';
    } finally {
      isLoading = false;
    }
  }

  // Generate contract
  async function generateContract() {
    if (!data.offer) {
      error = 'Please save the offer first';
      return;
    }

    if (enabledItems.length === 0) {
      error = 'Please enable at least one service item';
      return;
    }

    isLoading = true;
    error = null;
    success = null;

    try {
      const offerData = {
        _id: data.offer._id,
        customerId: data.customer._id,
        computerId: data.computer._id,
        items: offerItems,
        totalAmount,
        notes: offerNotes
      };

      const formData = new FormData();
      formData.append('offerData', JSON.stringify(offerData));

      const response = await fetch('?/generateContract', {
        method: 'POST',
        body: formData
      });

      const result = await response.json();

      if (result.success) {
        success = `Contract generated successfully (${result.contractNumber})`;
        offerStatus = 'Converted';
        // Optionally redirect to contract view
        // goto(`/contracts/${result.contractId}`);
      } else {
        error = result.error || 'Failed to generate contract';
      }
    } catch (err) {
      console.error('Error generating contract:', err);
      error = 'Failed to generate contract';
    } finally {
      isLoading = false;
    }
  }

  // Format currency
  function formatCurrency(amount) {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'EUR'
    }).format(amount || 0);
  }
</script>

<svelte:head>
  <title>Service Contract Offer - {data.customer?.companyName || 'Customer'}</title>
</svelte:head>

<div class="container">
  <!-- Header -->
  <div class="header">
    <button class="back-button" on:click={() => history.back()}>
      ← Back
    </button>

    <div class="header-center">
      <h1>Service Contract Offer</h1>
      <div class="header-info">
        <span class="offer-status status-{offerStatus.toLowerCase()}">{offerStatus}</span>
        {#if data.offer}
          <span class="offer-number">#{data.offer.offerNumber}</span>
        {/if}
      </div>
    </div>

    <div class="header-actions">
      <button
        class="btn btn-secondary"
        on:click={saveOffer}
        disabled={isLoading}
      >
        {isLoading ? 'Saving...' : 'Save Offer'}
      </button>

      {#if data.offer && offerStatus !== 'Converted'}
        <button
          class="btn btn-primary"
          on:click={generateContract}
          disabled={isLoading || enabledItems.length === 0}
        >
          Generate Contract
        </button>
      {/if}
    </div>
  </div>

  <!-- Messages -->
  {#if error}
    <div class="alert alert-error">
      {error}
    </div>
  {/if}

  {#if success}
    <div class="alert alert-success">
      {success}
    </div>
  {/if}

  <!-- Customer & Computer Info -->
  <div class="info-section">
    <div class="info-card">
      <h3>Customer Information</h3>
      <div class="info-grid">
        <div class="info-item">
          <span class="label">Company:</span>
          <span class="value">{data.customer.companyName}</span>
        </div>
        <div class="info-item">
          <span class="label">Type:</span>
          <span class="value">{data.customer.type}</span>
        </div>
        <div class="info-item">
          <span class="label">Location:</span>
          <span class="value">{data.customer.city}, {data.customer.country}</span>
        </div>
        <div class="info-item">
          <span class="label">Contact:</span>
          <span class="value">{data.customer.email}</span>
        </div>
      </div>
    </div>

    <div class="info-card">
      <h3>Computer Information</h3>
      <div class="info-grid">
        <div class="info-item">
          <span class="label">Name:</span>
          <span class="value">{data.computer.name || 'Unnamed'}</span>
        </div>
        <div class="info-item">
          <span class="label">Model:</span>
          <span class="value">{data.computer.model || 'N/A'}</span>
        </div>
        <div class="info-item">
          <span class="label">Serial:</span>
          <span class="value">{data.computer.serialNumber || 'N/A'}</span>
        </div>
        <div class="info-item">
          <span class="label">Product:</span>
          <span class="value">{data.computer.productDesignation || 'N/A'}</span>
        </div>
      </div>
    </div>
  </div>

  <!-- Offer Summary -->
  <div class="summary-section">
    <div class="summary-card">
      <h3>Offer Summary</h3>
      <div class="summary-stats">
        <div class="stat">
          <span class="stat-label">Total Items:</span>
          <span class="stat-value">{offerItems.length}</span>
        </div>
        <div class="stat">
          <span class="stat-label">Enabled:</span>
          <span class="stat-value">{enabledItems.length}</span>
        </div>
        <div class="stat">
          <span class="stat-label">Required:</span>
          <span class="stat-value">{requiredItems.length}</span>
        </div>
        <div class="stat total">
          <span class="stat-label">Total Amount:</span>
          <span class="stat-value">{formatCurrency(totalAmount)}</span>
        </div>
      </div>
    </div>
  </div>

  <!-- Service Items -->
  <div class="items-section">
    <div class="section-header">
      <h3>Service Items</h3>
      <button class="btn btn-secondary" on:click={addNewItem}>
        Add Custom Item
      </button>
    </div>

    <!-- Required Items -->
    {#if requiredItems.length > 0}
      <div class="items-category">
        <h4>Required Services</h4>
        <div class="items-table">
          <div class="table-header">
            <div class="col-enabled">Enabled</div>
            <div class="col-code">Service Code</div>
            <div class="col-description">Description</div>
            <div class="col-quantity">Qty</div>
            <div class="col-price">Unit Price</div>
            <div class="col-total">Total</div>
            <div class="col-actions">Actions</div>
          </div>

          {#each requiredItems as item (item._id)}
            <div class="table-row" class:disabled={!item.isEnabled}>
              <div class="col-enabled">
                <input
                  type="checkbox"
                  checked={item.isEnabled}
                  on:change={() => toggleItemEnabled(item._id)}
                />
              </div>
              <div class="col-code">{item.serviceCode}</div>
              <div class="col-description">
                <div class="description-main">{item.description}</div>
                <div class="description-sub">{item.activity}</div>
              </div>
              <div class="col-quantity">{item.quantity}</div>
              <div class="col-price">{formatCurrency(item.unitPrice)}</div>
              <div class="col-total">{formatCurrency(item.totalPrice)}</div>
              <div class="col-actions">
                <button class="btn-icon edit" on:click={() => editItem(item)} title="Edit">
                  ✏️
                </button>
                {#if item.source === 'Manual'}
                  <button class="btn-icon delete" on:click={() => removeItem(item._id)} title="Remove">
                    🗑️
                  </button>
                {/if}
              </div>
            </div>
          {/each}
        </div>
      </div>
    {/if}

    <!-- Optional Items -->
    {#if optionalItems.length > 0}
      <div class="items-category">
        <h4>Optional Services</h4>
        <div class="items-table">
          <div class="table-header">
            <div class="col-enabled">Enabled</div>
            <div class="col-code">Service Code</div>
            <div class="col-description">Description</div>
            <div class="col-quantity">Qty</div>
            <div class="col-price">Unit Price</div>
            <div class="col-total">Total</div>
            <div class="col-actions">Actions</div>
          </div>

          {#each optionalItems as item (item._id)}
            <div class="table-row" class:disabled={!item.isEnabled}>
              <div class="col-enabled">
                <input
                  type="checkbox"
                  checked={item.isEnabled}
                  on:change={() => toggleItemEnabled(item._id)}
                />
              </div>
              <div class="col-code">{item.serviceCode}</div>
              <div class="col-description">
                <div class="description-main">{item.description}</div>
                <div class="description-sub">{item.activity}</div>
              </div>
              <div class="col-quantity">{item.quantity}</div>
              <div class="col-price">{formatCurrency(item.unitPrice)}</div>
              <div class="col-total">{formatCurrency(item.totalPrice)}</div>
              <div class="col-actions">
                <button class="btn-icon edit" on:click={() => editItem(item)} title="Edit">
                  ✏️
                </button>
                {#if item.source === 'Manual'}
                  <button class="btn-icon delete" on:click={() => removeItem(item._id)} title="Remove">
                    🗑️
                  </button>
                {/if}
              </div>
            </div>
          {/each}
        </div>
      </div>
    {/if}

    <!-- Disabled Items -->
    {#if offerItems.filter(item => !item.isEnabled).length > 0}
      <div class="items-category collapsed">
        <h4>Disabled Items ({offerItems.filter(item => !item.isEnabled).length})</h4>
        <div class="items-table">
          <div class="table-header">
            <div class="col-enabled">Enabled</div>
            <div class="col-code">Service Code</div>
            <div class="col-description">Description</div>
            <div class="col-quantity">Qty</div>
            <div class="col-price">Unit Price</div>
            <div class="col-total">Total</div>
            <div class="col-actions">Actions</div>
          </div>

          {#each offerItems.filter(item => !item.isEnabled) as item (item._id)}
            <div class="table-row disabled">
              <div class="col-enabled">
                <input
                  type="checkbox"
                  checked={item.isEnabled}
                  on:change={() => toggleItemEnabled(item._id)}
                />
              </div>
              <div class="col-code">{item.serviceCode}</div>
              <div class="col-description">
                <div class="description-main">{item.description}</div>
                <div class="description-sub">{item.activity}</div>
              </div>
              <div class="col-quantity">{item.quantity}</div>
              <div class="col-price">{formatCurrency(item.unitPrice)}</div>
              <div class="col-total">{formatCurrency(item.totalPrice)}</div>
              <div class="col-actions">
                <button class="btn-icon edit" on:click={() => editItem(item)} title="Edit">
                  ✏️
                </button>
                {#if item.source === 'Manual'}
                  <button class="btn-icon delete" on:click={() => removeItem(item._id)} title="Remove">
                    🗑️
                  </button>
                {/if}
              </div>
            </div>
          {/each}
        </div>
      </div>
    {/if}
  </div>

  <!-- Notes Section -->
  <div class="notes-section">
    <h3>Offer Notes</h3>
    <textarea
      bind:value={offerNotes}
      placeholder="Add any additional notes or terms for this offer..."
      rows="4"
    ></textarea>
  </div>
</div>

<!-- Item Form Modal -->
{#if showItemForm && editingItem}
  <ServiceOfferItemForm
    item={editingItem}
    priceListItems={data.priceListItems}
    on:save={(e) => saveItem(e.detail)}
    on:cancel={() => { showItemForm = false; editingItem = null; }}
  />
{/if}

<style>
  .container {
    max-width: 1400px;
    margin: 0 auto;
    padding: 20px;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  }

  /* Header */
  .header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 30px;
    padding: 20px;
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  }

  .back-button {
    padding: 8px 16px;
    background: #f3f4f6;
    border: none;
    border-radius: 6px;
    cursor: pointer;
    font-size: 14px;
    color: #374151;
    transition: background-color 0.2s;
  }

  .back-button:hover {
    background: #e5e7eb;
  }

  .header-center {
    flex: 1;
    text-align: center;
  }

  .header-center h1 {
    margin: 0 0 8px 0;
    font-size: 24px;
    color: #111827;
  }

  .header-info {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 12px;
  }

  .offer-status {
    padding: 4px 12px;
    border-radius: 20px;
    font-size: 12px;
    font-weight: 600;
    text-transform: uppercase;
  }

  .status-draft {
    background: #fef3c7;
    color: #92400e;
  }

  .status-pending {
    background: #dbeafe;
    color: #1e40af;
  }

  .status-converted {
    background: #d1fae5;
    color: #065f46;
  }

  .offer-number {
    font-size: 14px;
    color: #6b7280;
    font-weight: 500;
  }

  .header-actions {
    display: flex;
    gap: 12px;
  }

  /* Buttons */
  .btn {
    padding: 8px 16px;
    border: none;
    border-radius: 6px;
    cursor: pointer;
    font-size: 14px;
    font-weight: 500;
    transition: all 0.2s;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    gap: 6px;
  }

  .btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
  }

  .btn-primary {
    background: #3b82f6;
    color: white;
  }

  .btn-primary:hover:not(:disabled) {
    background: #2563eb;
  }

  .btn-secondary {
    background: #f3f4f6;
    color: #374151;
    border: 1px solid #d1d5db;
  }

  .btn-secondary:hover:not(:disabled) {
    background: #e5e7eb;
  }

  .btn-icon {
    padding: 6px;
    background: none;
    border: none;
    cursor: pointer;
    border-radius: 4px;
    font-size: 14px;
    transition: background-color 0.2s;
  }

  .btn-icon:hover {
    background: #f3f4f6;
  }

  .btn-icon.edit:hover {
    background: #fef3c7;
  }

  .btn-icon.delete:hover {
    background: #fee2e2;
  }

  /* Alerts */
  .alert {
    padding: 12px 16px;
    border-radius: 6px;
    margin-bottom: 20px;
    font-size: 14px;
  }

  .alert-error {
    background: #fee2e2;
    color: #991b1b;
    border: 1px solid #fecaca;
  }

  .alert-success {
    background: #d1fae5;
    color: #065f46;
    border: 1px solid #a7f3d0;
  }

  /* Info Section */
  .info-section {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 20px;
    margin-bottom: 30px;
  }

  .info-card {
    background: white;
    padding: 20px;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  }

  .info-card h3 {
    margin: 0 0 16px 0;
    font-size: 18px;
    color: #111827;
  }

  .info-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 12px;
  }

  .info-item {
    display: flex;
    flex-direction: column;
    gap: 4px;
  }

  .info-item .label {
    font-size: 12px;
    color: #6b7280;
    font-weight: 500;
    text-transform: uppercase;
  }

  .info-item .value {
    font-size: 14px;
    color: #111827;
    font-weight: 500;
  }

  /* Summary Section */
  .summary-section {
    margin-bottom: 30px;
  }

  .summary-card {
    background: white;
    padding: 20px;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  }

  .summary-card h3 {
    margin: 0 0 16px 0;
    font-size: 18px;
    color: #111827;
  }

  .summary-stats {
    display: flex;
    gap: 24px;
    align-items: center;
  }

  .stat {
    display: flex;
    flex-direction: column;
    gap: 4px;
  }

  .stat.total {
    margin-left: auto;
    padding: 12px 20px;
    background: #f3f4f6;
    border-radius: 6px;
  }

  .stat-label {
    font-size: 12px;
    color: #6b7280;
    font-weight: 500;
    text-transform: uppercase;
  }

  .stat-value {
    font-size: 16px;
    color: #111827;
    font-weight: 600;
  }

  .stat.total .stat-value {
    font-size: 18px;
    color: #059669;
  }

  /* Items Section */
  .items-section {
    margin-bottom: 30px;
  }

  .section-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 20px;
  }

  .section-header h3 {
    margin: 0;
    font-size: 20px;
    color: #111827;
  }

  .items-category {
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    margin-bottom: 20px;
    overflow: hidden;
  }

  .items-category h4 {
    margin: 0;
    padding: 16px 20px;
    background: #f9fafb;
    border-bottom: 1px solid #e5e7eb;
    font-size: 16px;
    color: #374151;
    font-weight: 600;
  }

  .items-category.collapsed h4 {
    background: #f3f4f6;
    color: #6b7280;
  }

  .items-table {
    display: grid;
    grid-template-columns: 80px 120px 1fr 80px 120px 120px 100px;
    gap: 0;
  }

  .table-header {
    display: contents;
  }

  .table-header > div {
    padding: 12px 16px;
    background: #f9fafb;
    border-bottom: 2px solid #e5e7eb;
    font-size: 12px;
    font-weight: 600;
    color: #374151;
    text-transform: uppercase;
  }

  .table-row {
    display: contents;
  }

  .table-row > div {
    padding: 12px 16px;
    border-bottom: 1px solid #f3f4f6;
    display: flex;
    align-items: center;
    font-size: 14px;
    color: #111827;
  }

  .table-row.disabled > div {
    opacity: 0.5;
    background: #f9fafb;
  }

  .col-enabled {
    justify-content: center;
  }

  .col-enabled input[type="checkbox"] {
    width: 16px;
    height: 16px;
    cursor: pointer;
  }

  .col-code {
    font-family: 'Monaco', 'Menlo', monospace;
    font-size: 13px;
    color: #6366f1;
    font-weight: 500;
  }

  .col-description {
    flex-direction: column;
    align-items: flex-start;
    gap: 4px;
  }

  .description-main {
    font-weight: 500;
    color: #111827;
  }

  .description-sub {
    font-size: 12px;
    color: #6b7280;
  }

  .col-quantity,
  .col-price,
  .col-total {
    justify-content: flex-end;
    font-weight: 500;
  }

  .col-total {
    color: #059669;
  }

  .col-actions {
    justify-content: center;
    gap: 8px;
  }

  /* Notes Section */
  .notes-section {
    background: white;
    padding: 20px;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    margin-bottom: 30px;
  }

  .notes-section h3 {
    margin: 0 0 16px 0;
    font-size: 18px;
    color: #111827;
  }

  .notes-section textarea {
    width: 100%;
    padding: 12px;
    border: 1px solid #d1d5db;
    border-radius: 6px;
    font-size: 14px;
    font-family: inherit;
    resize: vertical;
    min-height: 100px;
  }

  .notes-section textarea:focus {
    outline: none;
    border-color: #3b82f6;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
  }

  /* Responsive Design */
  @media (max-width: 1200px) {
    .items-table {
      grid-template-columns: 60px 100px 1fr 60px 100px 100px 80px;
    }
  }

  @media (max-width: 768px) {
    .container {
      padding: 10px;
    }

    .header {
      flex-direction: column;
      gap: 16px;
      text-align: center;
    }

    .header-actions {
      justify-content: center;
    }

    .info-section {
      grid-template-columns: 1fr;
    }

    .summary-stats {
      flex-direction: column;
      align-items: stretch;
      gap: 12px;
    }

    .stat.total {
      margin-left: 0;
    }

    .items-table {
      grid-template-columns: 1fr;
      gap: 8px;
    }

    .table-header {
      display: none;
    }

    .table-row > div {
      padding: 8px 12px;
      border: 1px solid #e5e7eb;
      border-radius: 4px;
      margin-bottom: 4px;
    }

    .table-row > div:before {
      content: attr(data-label) ': ';
      font-weight: 600;
      color: #6b7280;
      font-size: 12px;
      text-transform: uppercase;
    }
  }
</style>