<script>
  import QuotationGrid from './grids/QuotationGrid.svelte';
  import { createEventDispatcher } from 'svelte';

  const dispatch = createEventDispatcher();

  // Props
  export let quotationPackages = null;
  export let computer = null;

  // Sample package offering data (levels A, B, C)
  const packageOfferings = [
    { level: 'A', name: 'N/A', status: 'N/A' },
    { level: 'B', name: 'N/A', status: 'N/A' },
    { level: 'C', name: 'N/A', status: 'N/A' }
  ];

  // Transform quotation data to match the individual service structure
  function transformToIndividualServices(quotationPackages) {
    if (!quotationPackages) return [];
    
    const services = [];
    let serviceIndex = 1;

    // Base Contract Offering
    if (quotationPackages.baseContract) {
      quotationPackages.baseContract.forEach(service => {
        services.push({
          level: serviceIndex,
          packageName: 'Base Contract Offering',
          serviceId: service.serviceCode || '4.1',
          ServiceActivity: service.ServiceActivity || 'Parts Supply on Request',
          includedInPackage: service.cost === 0,
          required: service.required,
          includeInOffer: service.includeInOffer,
          cost: service.cost || 0,
          id: service.id
        });
        serviceIndex++;
      });
    }

    // Dealer Add-Ons
    if (quotationPackages.repairPackages) {
      quotationPackages.repairPackages.forEach(service => {
        services.push({
          level: serviceIndex,
          packageName: 'Dealer Add-Ons',
          serviceId: service.serviceCode || '1.2',
          ServiceActivity: service.ServiceActivity || 'Inspections',
          includedInPackage: service.cost === 0,
          required: service.required,
          includeInOffer: service.includeInOffer,
          cost: service.cost || 0,
          id: service.id
        });
        serviceIndex++;
      });
    }

    // Support Services
    if (quotationPackages.supportServices) {
      quotationPackages.supportServices.forEach(service => {
        services.push({
          level: serviceIndex,
          packageName: 'Support (Self Service)',
          serviceId: service.serviceCode || '2.1',
          ServiceActivity: service.ServiceActivity || 'Oil Sampling',
          includedInPackage: service.cost === 0,
          required: service.required,
          includeInOffer: service.includeInOffer,
          cost: service.cost || 0,
          id: service.id
        });
        serviceIndex++;
      });
    }

    // Replacement Services
    if (quotationPackages.replacementServices) {
      quotationPackages.replacementServices.forEach(service => {
        services.push({
          level: serviceIndex,
          packageName: 'Retirement Plan',
          serviceId: service.serviceCode || '10.1',
          ServiceActivity: service.ServiceActivity || 'Engine Replacement',
          includedInPackage: service.cost === 0,
          required: service.required,
          includeInOffer: service.includeInOffer,
          cost: service.cost || 0,
          id: service.id
        });
        serviceIndex++;
      });
    }

    return services;
  }

  // Get grouped services summary
  function getGroupedServices(individualServices) {
    const groups = {};
    
    individualServices.forEach(service => {
      if (service.includeInOffer) {
        const packageName = service.packageName;
        if (!groups[packageName]) {
          groups[packageName] = [];
        }
        groups[packageName].push(service);
      }
    });

    return Object.entries(groups).map(([packageName, services], index) => ({
      level: index + 1,
      packageName: `${packageName} (Fixed)`,
      services: services.length
    }));
  }

  $: individualServices = transformToIndividualServices(quotationPackages);
  $: groupedServices = getGroupedServices(individualServices);

  // Event handlers
  function handlePackageToggle(event) {
    dispatch('togglePackage', event.detail);
  }

  function handleServiceSelect(event) {
    dispatch('selectService', event.detail);
  }

  // Calculate total cost
  function calculateTotalCost(services) {
    return services
      .filter(service => service.includeInOffer)
      .reduce((total, service) => total + (service.cost || 0), 0);
  }

  $: totalCost = calculateTotalCost(individualServices);
</script>

<div class="enhanced-service-offering">
  <h2>Service Offering</h2>
  
  <!-- Package Offering Section -->
  <div class="section">
    <h3>Package offering</h3>
    <QuotationGrid 
      packages={packageOfferings}
      isPackageOffering={true}
      showCosts={false}
      showOemImporter={false}
      showFleetOwner={false}
      showLevel={false}
      showRequired={false}
      on:togglePackage={handlePackageToggle}
    />
  </div>

  <!-- Individual Service Section -->
  <div class="section">
    <h3>Individual service</h3>
    <QuotationGrid 
      packages={individualServices}
      isPackageOffering={false}
      showServiceDropdown={true}
      showCosts={true}
      showOemImporter={false}
      showFleetOwner={false}
      showLevel={false}
      showRequired={true}
      on:togglePackage={handlePackageToggle}
      on:selectService={handleServiceSelect}
    />
  </div>

  <!-- Total Cost Section -->
  <div class="total-section">
    <div class="total-row">
      <span class="total-label">Total cost in quote</span>
      <span class="total-value">#REF!</span>
    </div>
  </div>

  <!-- Grouped Services Section -->
  <div class="section">
    <h3>Grouped services</h3>
    <div class="grouped-services">
      <div class="grouped-header">
        <div class="col">Level</div>
        <div class="col">Package</div>
      </div>
      {#each groupedServices as group}
        <div class="grouped-row">
          <div class="col">{group.level}</div>
          <div class="col">{group.packageName}</div>
        </div>
      {/each}
    </div>
  </div>
</div>

<style>
  .enhanced-service-offering {
    padding: 1rem;
    max-width: 1400px;
    margin: 0 auto;
  }

  .section {
    margin-bottom: 2rem;
  }

  h2 {
    color: #333;
    margin-bottom: 1.5rem;
    border-bottom: 2px solid #e0e0e0;
    padding-bottom: 0.5rem;
  }

  h3 {
    color: #333;
    margin-bottom: 1rem;
    font-size: 1.1rem;
    font-weight: 600;
  }

  .total-section {
    margin: 1.5rem 0;
    padding: 1rem;
    background-color: #f9f9f9;
    border: 1px solid #e0e0e0;
    border-radius: 4px;
  }

  .total-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-weight: 600;
  }

  .total-label {
    color: #333;
  }

  .total-value {
    color: #2e7d32;
    font-size: 1.1rem;
  }

  .grouped-services {
    border: 1px solid #e0e0e0;
    border-radius: 4px;
    overflow: hidden;
  }

  .grouped-header {
    display: grid;
    grid-template-columns: minmax(60px, 0.5fr) minmax(200px, 2fr);
    background-color: #f5f5f5;
    font-weight: 600;
    border-bottom: 2px solid #ddd;
  }

  .grouped-row {
    display: grid;
    grid-template-columns: minmax(60px, 0.5fr) minmax(200px, 2fr);
    border-bottom: 1px solid #e0e0e0;
  }

  .grouped-row:nth-child(even) {
    background-color: #fafafa;
  }

  .col {
    padding: 0.75rem 0.5rem;
    display: flex;
    align-items: center;
  }

  @media (max-width: 768px) {
    .enhanced-service-offering {
      padding: 0.5rem;
    }

    .total-row {
      flex-direction: column;
      gap: 0.5rem;
      align-items: flex-start;
    }

    .grouped-header,
    .grouped-row {
      grid-template-columns: 1fr;
    }
  }
</style>
