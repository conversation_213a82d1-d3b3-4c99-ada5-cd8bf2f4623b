import { error } from '@sveltejs/kit';
import { ObjectId } from 'mongodb';
import { getCollection } from '$lib/db/mongo';
import serviceContractDB, { COLLECTIONS } from '$lib/server/serviceContractDB.js';

/**
 * @typedef {Object} ServiceOfferItem
 * @property {string} _id
 * @property {string} serviceCode
 * @property {string} description
 * @property {string} activity
 * @property {number} quantity
 * @property {number} unitPrice
 * @property {number} totalPrice
 * @property {boolean} isEnabled
 * @property {boolean} isRequired
 * @property {string} category
 * @property {string} notes
 */

/**
 * @typedef {Object} ServiceOffer
 * @property {string} _id
 * @property {string} customerId
 * @property {string} computerId
 * @property {string} offerNumber
 * @property {string} status
 * @property {Date} validUntil
 * @property {ServiceOfferItem[]} items
 * @property {number} totalAmount
 * @property {string} notes
 * @property {Date} createdAt
 * @property {Date} updatedAt
 */

/** @type {import('./$types').PageServerLoad} */
export async function load({ url }) {
  try {
    const computerId = url.searchParams.get('computerId');
    const productDesignation = url.searchParams.get('productDesignation');
    
    if (!computerId) {
      throw error(400, 'Computer ID is required');
    }

    // Get computer and customer information
    const computerCollection = await getCollection('CustomerComputers');
    const computer = await computerCollection.findOne({ _id: new ObjectId(computerId) });
    
    if (!computer) {
      throw error(404, 'Computer not found');
    }

    const customerCollection = await getCollection('Customers');
    const customer = await customerCollection.findOne({ _id: new ObjectId(computer.customerId) });
    
    if (!customer) {
      throw error(404, 'Customer not found');
    }

    // Get existing service offer for this computer if any
    const serviceOfferCollection = await getCollection('ServiceOffers');
    const existingOffer = await serviceOfferCollection.findOne({ 
      computerId: new ObjectId(computerId),
      status: { $in: ['Draft', 'Pending'] }
    });

    // Get available service items from various collections
    const serviceCodeCollection = await getCollection('ServiceCodeAndActionType');
    const availableServices = await serviceCodeCollection.find({
      ProductValidityGroup: productDesignation || computer.productDesignation
    }).toArray();

    // Get service plan items
    const servicePlanCollection = await getCollection('ServicePlanProductDesignation');
    const servicePlanItems = await servicePlanCollection.find({
      productDesignation: productDesignation || computer.productDesignation
    }).toArray();

    // Get pricing information
    const priceListCollection = await getCollection('PriceLIst');
    const priceListItems = await priceListCollection.find({}).toArray();

    // Transform and combine service items
    const serviceItems = [
      ...availableServices.map(service => ({
        _id: service._id.toString(),
        serviceCode: service.ServiceCode || '',
        description: service.ServiceActivityLabel || service.ActivityPurpose || '',
        activity: service.ActionType || '',
        quantity: service.Quantity || 1,
        unitPrice: 0, // Will be calculated based on pricing rules
        totalPrice: 0,
        isEnabled: true,
        isRequired: false,
        category: 'Service',
        notes: '',
        source: 'ServiceCode'
      })),
      ...servicePlanItems.map(item => ({
        _id: item._id.toString(),
        serviceCode: item.serviceCode || '',
        description: item.actionType || '',
        activity: item.activityPurpose || '',
        quantity: 1,
        unitPrice: 0,
        totalPrice: 0,
        isEnabled: true,
        isRequired: item.frequency === 'Required',
        category: 'Maintenance',
        notes: '',
        source: 'ServicePlan'
      }))
    ];

    // If there's an existing offer, merge with available services
    let offerItems = serviceItems;
    if (existingOffer && existingOffer.items) {
      const existingItemsMap = new Map(existingOffer.items.map(item => [item.serviceCode, item]));
      
      offerItems = serviceItems.map(item => {
        const existing = existingItemsMap.get(item.serviceCode);
        if (existing) {
          return {
            ...item,
            ...existing,
            _id: existing._id || item._id
          };
        }
        return item;
      });
    }

    return {
      computer: {
        ...computer,
        _id: computer._id.toString(),
        customerId: computer.customerId.toString()
      },
      customer: {
        ...customer,
        _id: customer._id.toString()
      },
      offer: existingOffer ? {
        ...existingOffer,
        _id: existingOffer._id.toString(),
        customerId: existingOffer.customerId.toString(),
        computerId: existingOffer.computerId.toString()
      } : null,
      serviceItems: offerItems,
      priceListItems: priceListItems.map(item => ({
        ...item,
        _id: item._id.toString()
      }))
    };
  } catch (err) {
    console.error('Error loading service offer data:', err);
    throw error(500, 'Failed to load service offer data');
  }
}

/** @type {import('./$types').Actions} */
export const actions = {
  createOffer: async ({ request }) => {
    try {
      const formData = await request.formData();
      const offerData = JSON.parse(formData.get('offerData'));
      
      const serviceOfferCollection = await getCollection('ServiceOffers');
      
      // Generate offer number
      const offerCount = await serviceOfferCollection.countDocuments();
      const offerNumber = `SO-${new Date().getFullYear()}-${String(offerCount + 1).padStart(4, '0')}`;
      
      const newOffer = {
        customerId: new ObjectId(offerData.customerId),
        computerId: new ObjectId(offerData.computerId),
        offerNumber,
        status: 'Draft',
        validUntil: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000), // 30 days from now
        items: offerData.items,
        totalAmount: offerData.totalAmount,
        notes: offerData.notes || '',
        createdAt: new Date(),
        updatedAt: new Date()
      };
      
      const result = await serviceOfferCollection.insertOne(newOffer);
      
      return {
        success: true,
        offerId: result.insertedId.toString(),
        offerNumber
      };
    } catch (err) {
      console.error('Error creating service offer:', err);
      return {
        success: false,
        error: 'Failed to create service offer'
      };
    }
  },

  updateOffer: async ({ request }) => {
    try {
      const formData = await request.formData();
      const offerData = JSON.parse(formData.get('offerData'));
      
      const serviceOfferCollection = await getCollection('ServiceOffers');
      
      const updateData = {
        items: offerData.items,
        totalAmount: offerData.totalAmount,
        notes: offerData.notes || '',
        updatedAt: new Date()
      };
      
      const result = await serviceOfferCollection.updateOne(
        { _id: new ObjectId(offerData._id) },
        { $set: updateData }
      );
      
      return {
        success: result.modifiedCount > 0,
        error: result.modifiedCount === 0 ? 'No changes made' : null
      };
    } catch (err) {
      console.error('Error updating service offer:', err);
      return {
        success: false,
        error: 'Failed to update service offer'
      };
    }
  },

  generateContract: async ({ request }) => {
    try {
      const formData = await request.formData();
      const offerData = JSON.parse(formData.get('offerData'));
      
      // Create contract header
      const contractHeader = {
        customerId: new ObjectId(offerData.customerId),
        computerId: new ObjectId(offerData.computerId),
        contractNumber: `SC-${new Date().getFullYear()}-${String(Date.now()).slice(-6)}`,
        status: 'Draft',
        startDate: new Date(),
        endDate: new Date(Date.now() + 365 * 24 * 60 * 60 * 1000), // 1 year
        totalAmount: offerData.totalAmount,
        offerId: new ObjectId(offerData._id),
        createdAt: new Date(),
        updatedAt: new Date()
      };
      
      const contractHeaderCollection = await getCollection('ContractHeaders');
      const headerResult = await contractHeaderCollection.insertOne(contractHeader);
      
      // Create contract lines
      const contractLines = offerData.items
        .filter(item => item.isEnabled)
        .map((item, index) => ({
          contractId: headerResult.insertedId,
          lineNumber: index + 1,
          serviceCode: item.serviceCode,
          description: item.description,
          quantity: item.quantity,
          unitPrice: item.unitPrice,
          totalPrice: item.totalPrice,
          isRequired: item.isRequired,
          category: item.category,
          notes: item.notes,
          createdAt: new Date(),
          updatedAt: new Date()
        }));
      
      const contractLinesCollection = await getCollection('ContractLines');
      await contractLinesCollection.insertMany(contractLines);
      
      // Update offer status
      const serviceOfferCollection = await getCollection('ServiceOffers');
      await serviceOfferCollection.updateOne(
        { _id: new ObjectId(offerData._id) },
        { $set: { status: 'Converted', updatedAt: new Date() } }
      );
      
      return {
        success: true,
        contractId: headerResult.insertedId.toString(),
        contractNumber: contractHeader.contractNumber
      };
    } catch (err) {
      console.error('Error generating contract:', err);
      return {
        success: false,
        error: 'Failed to generate contract'
      };
    }
  }
};
